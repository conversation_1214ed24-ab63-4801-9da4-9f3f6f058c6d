// Scroll Progress Bar
function updateScrollProgress() {
  const scrollTop = window.pageYOffset;
  const docHeight = document.body.scrollHeight - window.innerHeight;
  const scrollPercent = (scrollTop / docHeight) * 100;
  document.querySelector(".scroll-progress").style.width = scrollPercent + "%";
}

window.addEventListener("scroll", updateScrollProgress);

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute("href"));
    if (target) {
      target.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  });
});

// Scroll animation
const observerOptions = {
  threshold: 0.1,
  rootMargin: "0px 0px -50px 0px",
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      entry.target.classList.add("visible");
    }
  });
}, observerOptions);

document.querySelectorAll(".fade-in").forEach((el) => {
  observer.observe(el);
});

// Typing Animation
function typeWriter(element, words, speed = 100) {
  let wordIndex = 0;
  let charIndex = 0;
  let isDeleting = false;

  function type() {
    const currentWord = words[wordIndex];

    if (isDeleting) {
      element.textContent = currentWord.substring(0, charIndex - 1);
      charIndex--;
    } else {
      element.textContent = currentWord.substring(0, charIndex + 1);
      charIndex++;
    }

    let typeSpeed = speed;

    if (isDeleting) {
      typeSpeed /= 2;
    }

    if (!isDeleting && charIndex === currentWord.length) {
      typeSpeed = 2000; // Pause at end
      isDeleting = true;
    } else if (isDeleting && charIndex === 0) {
      isDeleting = false;
      wordIndex = (wordIndex + 1) % words.length;
    }

    setTimeout(type, typeSpeed);
  }

  type();
}

// Initialize typing animation
document.addEventListener("DOMContentLoaded", function () {
  const typingElement = document.querySelector(".typing-text");
  if (typingElement) {
    const words = typingElement.getAttribute("data-words").split(",");
    typeWriter(typingElement, words);
  }
});

// Counter Animation
function animateCounter(element, target, duration = 2000) {
  let start = 0;
  const increment = target / (duration / 16);

  function updateCounter() {
    start += increment;
    if (start < target) {
      element.textContent = Math.floor(start).toLocaleString();
      requestAnimationFrame(updateCounter);
    } else {
      element.textContent = target.toLocaleString();
    }
  }

  updateCounter();
}

// Initialize counters when they come into view
const counterObserver = new IntersectionObserver((entries) => {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      const counter = entry.target;
      const target = parseInt(counter.getAttribute("data-target"));
      animateCounter(counter, target);
      counterObserver.unobserve(counter);
    }
  });
});

document.querySelectorAll(".stat-number").forEach((counter) => {
  counterObserver.observe(counter);
});

// Add some interactive hover effects
document.querySelectorAll(".cta-button").forEach((button) => {
  button.addEventListener("mouseenter", function () {
    this.style.transform = "translateY(-3px) scale(1.02)";
  });

  button.addEventListener("mouseleave", function () {
    this.style.transform = "translateY(0) scale(1)";
  });
});

// Performance optimization: Throttle scroll events
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Apply throttling to scroll progress
window.removeEventListener("scroll", updateScrollProgress);
window.addEventListener("scroll", throttle(updateScrollProgress, 16));

// Lazy loading for images (if any are added later)
function lazyLoadImages() {
  const images = document.querySelectorAll("img[data-src]");
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove("lazy");
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach((img) => imageObserver.observe(img));
}

// Enhanced CTA click tracking with loading states
document.querySelectorAll(".cta-button").forEach((button) => {
  button.addEventListener("click", function (e) {
    e.preventDefault();

    // Add loading state
    const originalText = this.textContent;
    this.textContent = "⏳ Loading...";
    this.style.pointerEvents = "none";

    // Simulate processing time
    setTimeout(() => {
      this.textContent = originalText;
      this.style.pointerEvents = "auto";

      // Analytics tracking
      console.log("CTA clicked:", originalText);

      // Show success message
      alert(
        "🎉 Great choice! In a real implementation, this would redirect to your lead magnet signup or direct download."
      );
    }, 1500);
  });
});

// Reduce motion for users who prefer it
if (window.matchMedia("(prefers-reduced-motion: reduce)").matches) {
  document.documentElement.style.setProperty("--animation-duration", "0.01ms");
  document.documentElement.style.setProperty("--animation-delay", "0.01ms");
}

// Initialize performance optimizations
document.addEventListener("DOMContentLoaded", function () {
  lazyLoadImages();

  // Add performance observer for monitoring
  if ("PerformanceObserver" in window) {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "largest-contentful-paint") {
          console.log("LCP:", entry.startTime);
        }
      }
    });
    observer.observe({ entryTypes: ["largest-contentful-paint"] });
  }
});
