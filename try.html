<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Join <PERSON><PERSON> Mission Umrah - Easy installments of Rs. 2786 only! Monthly draws for sponsored Umrah packages."
    />
    <meta
      name="keywords"
      content="Umrah, Hajj, Islamic Tours, Pakistan, Saudi Arabia, Mecca, Medina"
    />
    <meta name="author" content="Yaa Khwaja Tours" />
    <meta property="og:title" content="Yaa Khwaja Tours - Mission Umrah" />
    <meta
      property="og:description"
      content="Now everyone can perform Umrah in easy installments of Rs. 2786 only!"
    />
    <meta property="og:type" content="website" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <title><PERSON><PERSON> - Mission Umrah</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

      :root {
        --primary-color: #6366f1;
        --secondary-color: #8b5cf6;
        --accent-color: #f59e0b;
        --success-color: #10b981;
        --danger-color: #ef4444;
        --dark-color: #1f2937;
        --light-color: #f9fafb;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --border-radius: 16px;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
          0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
          0 8px 10px -6px rgb(0 0 0 / 0.1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html {
        scroll-behavior: smooth;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        line-height: 1.6;
        color: var(--text-primary);
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 50%,
          #f093fb 100%
        );
        background-attachment: fixed;
        min-height: 100vh;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 2rem 1rem;
        text-align: center;
      }

      .container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 20% 80%,
            rgba(120, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 40%,
            rgba(120, 219, 255, 0.2) 0%,
            transparent 50%
          );
        pointer-events: none;
      }

      .full.screen {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .wrapper {
        position: relative;
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
        z-index: 1;
      }

      .inner {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        padding: 4rem 3rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
        transition: all 0.3s ease;
      }

      .inner:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
      }

      h1,
      h2 {
        font-weight: 800;
        margin-bottom: 1.5rem;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      h1 {
        font-size: clamp(2.5rem, 5vw, 4rem);
        line-height: 1.1;
        letter-spacing: -0.02em;
      }

      h2 {
        font-size: clamp(2rem, 4vw, 3rem);
        line-height: 1.2;
        letter-spacing: -0.01em;
      }

      p {
        margin-bottom: 1.5rem;
        font-size: 1.125rem;
        line-height: 1.7;
        color: var(--text-secondary);
        font-weight: 400;
      }

      .style2 {
        color: var(--secondary-color);
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin-bottom: 1rem;
      }

      .style3 {
        color: var(--text-secondary);
        font-size: 1.125rem;
        font-weight: 400;
        line-height: 1.8;
      }

      mark {
        background: linear-gradient(135deg, var(--accent-color), #f97316);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 700;
        font-size: 1.25rem;
        box-shadow: var(--shadow-lg);
        display: inline-block;
        transform: rotate(-2deg);
        transition: transform 0.3s ease;
      }

      mark:hover {
        transform: rotate(0deg) scale(1.05);
      }

      .icons {
        list-style: none;
        margin: 3rem 0;
        display: flex;
        justify-content: center;
        gap: 1rem;
      }

      .icons li {
        display: inline-block;
      }

      .icons a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        color: white;
        border-radius: 50%;
        text-decoration: none;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
      }

      .icons a::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .icons a:hover::before {
        opacity: 1;
      }

      .icons a:hover {
        transform: translateY(-8px) scale(1.1);
        box-shadow: 0 20px 40px rgba(99, 102, 241, 0.4);
      }

      .icons svg {
        width: 28px;
        height: 28px;
        fill: currentColor;
        z-index: 1;
      }

      .buttons {
        list-style: none;
        margin: 3rem 0;
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .buttons li {
        display: inline-block;
      }

      .button {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1.25rem 2.5rem;
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.125rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
        border: none;
        cursor: pointer;
      }

      .button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s ease;
      }

      .button:hover::before {
        left: 100%;
      }

      .button:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 20px 40px rgba(16, 185, 129, 0.4);
      }

      .button:active {
        transform: translateY(-2px) scale(0.98);
      }

      .button svg {
        width: 22px;
        height: 22px;
        fill: currentColor;
        transition: transform 0.3s ease;
      }

      .button:hover svg {
        transform: translateX(4px);
      }

      .label {
        display: none;
      }

      .icc-credits {
        text-align: center;
        padding: 3rem 2rem;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        font-weight: 400;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 2rem;
      }

      .icc-credits a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
      }

      .icc-credits a::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        transition: width 0.3s ease;
      }

      .icc-credits a:hover {
        color: white;
      }

      .icc-credits a:hover::after {
        width: 100%;
      }

      /* Container Variants */
      .style1.container {
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 50%,
          #f093fb 100%
        );
      }

      .style2.container {
        background: linear-gradient(
          135deg,
          #f093fb 0%,
          #f5576c 50%,
          #764ba2 100%
        );
      }

      /* Animation States */
      .is-ready {
        opacity: 1;
      }

      .fade-in {
        animation: fadeIn 1s ease-out;
      }

      /* Keyframe Animations */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(40px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.8;
        }
      }

      /* Floating Animation for Icons */
      .icons a {
        animation: float 3s ease-in-out infinite;
      }

      .icons a:nth-child(2) {
        animation-delay: 0.5s;
      }

      .icons a:nth-child(3) {
        animation-delay: 1s;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .wrapper {
          padding: 1rem;
        }

        .inner {
          padding: 2.5rem 2rem;
        }

        .container {
          padding: 1rem;
        }

        .buttons {
          flex-direction: column;
          align-items: center;
        }

        .button {
          width: 100%;
          max-width: 300px;
          justify-content: center;
        }

        .icons {
          gap: 0.5rem;
        }

        .icons a {
          width: 56px;
          height: 56px;
        }

        .icons svg {
          width: 24px;
          height: 24px;
        }
      }

      @media (max-width: 480px) {
        .inner {
          padding: 2rem 1.5rem;
        }

        .wrapper {
          padding: 0.5rem;
        }

        mark {
          font-size: 1.125rem;
          padding: 0.5rem 1.25rem;
        }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        .inner {
          background: rgba(17, 24, 39, 0.95);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .style3 {
          color: #9ca3af;
        }
      }

      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }

        .icons a {
          animation: none;
        }
      }

      /* Loading Animation */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          #667eea 0%,
          #764ba2 50%,
          #f093fb 100%
        );
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.5s ease;
      }

      .loading-overlay.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Particle Effect */
      .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
      }

      .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        animation: float-particle 6s ease-in-out infinite;
      }

      @keyframes float-particle {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
          opacity: 0;
        }
        10%,
        90% {
          opacity: 1;
        }
        50% {
          transform: translateY(-100px) rotate(180deg);
        }
      }

      /* Enhanced Typography */
      .highlight-text {
        background: linear-gradient(135deg, var(--accent-color), #f97316);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
      }

      /* Improved Spacing */
      .section-spacing {
        margin: 4rem 0;
      }

      /* Enhanced Cards */
      .card-enhanced {
        position: relative;
        overflow: hidden;
      }

      .card-enhanced::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.5),
          transparent
        );
      }
    </style>
  </head>
  <body class="is-ready">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="loading-spinner"></div>
    </div>

    <!-- Particle Effect -->
    <div class="particles" id="particles"></div>

    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 40 40"
      display="none"
      width="0"
      height="0"
    >
      <symbol id="icon-80dc50be409b6c0a58c1f62335c07e7d" viewBox="0 0 40 40">
        <path
          d="M12.9,32.4L25.3,20L13,7.6c-0.2-0.2-0.2-0.5,0-0.7l1.6-1.6c0.2-0.2,0.5-0.2,0.7,0L29.8,20L15.1,34.6c-0.2,0.2-0.5,0.2-0.7,0 l-1.6-1.6C12.7,32.9,12.7,32.6,12.9,32.4z"
        ></path>
      </symbol>
      <symbol id="icon-ecbd7520f72820fb3c1bfce19d8ed9ce" viewBox="0 0 40 40">
        <path
          d="M5.2,14.6l1.5-1.5c0.2-0.2,0.5-0.2,0.7,0l12.4,12.4l12.4-12.4c0.2-0.2,0.5-0.2,0.7,0l1.6,1.6c0.2,0.2,0.2,0.5,0,0.7L19.8,30 L5.2,15.4C5,15.2,5,14.8,5.2,14.6z"
        ></path>
      </symbol>
    </svg>
    <div id="wrapper">
      <div id="main">
        <div class="inner">
          <div
            id="container03"
            class="style1 container default full screen"
            style="
              --element-top: 0;
              --onvisible-speed: 1s;
              --onvisible-background-color: rgba(0, 0, 0, 0.001);
            "
          >
            <div class="wrapper">
              <div
                class="inner card-enhanced"
                data-onvisible-trigger="1"
                style="opacity: 1; transform-origin: 50% 50%; transform: none"
              >
                <p
                  id="text03"
                  class="style2"
                  style="opacity: 1; transform: none"
                >
                  <mark>Yaa Khwaja Tours</mark>
                </p>
                <h1
                  id="text04"
                  class="style1"
                  style="opacity: 1; transform: none"
                >
                  # mission umrah
                </h1>
                <p
                  id="text08"
                  class="style3"
                  style="opacity: 1; transform: none"
                >
                  <span class="p"
                    >Now everyone can perform Umrah in easy installments of Rs.
                    2786 only!<br />
                    Connect now to know more.</span
                  >
                </p>
                <ul
                  id="icons01"
                  class="style1 icons"
                  style="opacity: 1; transform: none"
                >
                  <li>
                    <a
                      class="n01"
                      onclick="_nextScrollPoint(event);"
                      role="button"
                      ><svg aria-labelledby="icons01-icon-1-title">
                        <title id="icons01-icon-1-title">Chevron Down</title>
                        <use
                          xlink:href="#icon-ecbd7520f72820fb3c1bfce19d8ed9ce"
                        ></use></svg
                      ><span class="label">Chevron Down</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div
            id="container04"
            data-scroll-id="one"
            data-scroll-behavior="default"
            data-scroll-offset="0"
            data-scroll-speed="2"
            data-scroll-invisible="1"
            class="style2 container default full screen"
          >
            <div class="wrapper">
              <div
                class="inner"
                data-onvisible-trigger="1"
                style="opacity: 1; transform-origin: 50% 50%; transform: none"
              >
                <p
                  id="text12"
                  class="style2"
                  style="opacity: 1; transform: none"
                >
                  Become a winner
                </p>
                <h2
                  id="text13"
                  class="style1"
                  style="opacity: 1; transform: none"
                >
                  by paying only Rs 2786
                </h2>
                <p
                  id="text15"
                  class="style3"
                  style="opacity: 1; transform: none"
                >
                  <span class="p"
                    >Pay easy emi and become part of a blessed Draw<br />
                    the winner will be sponsored Umrah at only Rs. 2786<br />
                    no further payment required!</span
                  >
                </p>
                <ul
                  id="icons04"
                  class="style1 icons"
                  style="opacity: 1; transform: none"
                >
                  <li>
                    <a
                      class="n01"
                      onclick="_nextScrollPoint(event);"
                      role="button"
                      ><svg aria-labelledby="icons04-icon-1-title">
                        <title id="icons04-icon-1-title">Chevron Down</title>
                        <use
                          xlink:href="#icon-ecbd7520f72820fb3c1bfce19d8ed9ce"
                        ></use></svg
                      ><span class="label">Chevron Down</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div
            id="container02"
            data-scroll-id="two"
            data-scroll-behavior="default"
            data-scroll-offset="0"
            data-scroll-speed="2"
            data-scroll-invisible="1"
            class="style1 container default full screen"
            style="
              --element-top: 1390;
              --onvisible-speed: 1s;
              --onvisible-background-color: rgba(0, 0, 0, 0.001);
            "
          >
            <div class="wrapper">
              <div
                class="inner"
                data-onvisible-trigger="1"
                style="opacity: 1; transform-origin: 50% 50%; transform: none"
              >
                <p
                  id="text09"
                  class="style2"
                  style="opacity: 1; transform: none"
                >
                  accompain a family member
                </p>
                <h2
                  id="text10"
                  class="style1"
                  style="opacity: 1; transform: none"
                >
                  in just Rs 35000/-
                </h2>
                <p
                  id="text11"
                  class="style3"
                  style="opacity: 1; transform: none"
                >
                  the winner can accompain a family member by paying only Rs.
                  35000/-
                </p>
                <ul
                  id="icons02"
                  class="style1 icons"
                  style="opacity: 1; transform: none"
                >
                  <li>
                    <a
                      class="n01"
                      onclick="_nextScrollPoint(event);"
                      role="button"
                      ><svg aria-labelledby="icons02-icon-1-title">
                        <title id="icons02-icon-1-title">Chevron Down</title>
                        <use
                          xlink:href="#icon-ecbd7520f72820fb3c1bfce19d8ed9ce"
                        ></use></svg
                      ><span class="label">Chevron Down</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div
            id="container01"
            data-scroll-id="three"
            data-scroll-behavior="default"
            data-scroll-offset="0"
            data-scroll-speed="2"
            data-scroll-invisible="1"
            class="style2 container default full screen"
          >
            <div class="wrapper">
              <div
                class="inner"
                data-onvisible-trigger="1"
                style="opacity: 1; transform-origin: 50% 50%; transform: none"
              >
                <p
                  id="text05"
                  class="style2"
                  style="opacity: 1; transform: none"
                >
                  Regular Draws
                </p>
                <h2
                  id="text06"
                  class="style1"
                  style="opacity: 1; transform: none"
                >
                  Each Month!
                </h2>
                <p
                  id="text07"
                  class="style3"
                  style="opacity: 1; transform: none"
                >
                  if you are not the winner on first draw, don't worry! pay
                  another emi and enter into another draw and become winner. Get
                  every benefit as earlier. The cycle continues for 33 months.
                  Each month will have a winner and a companion...!
                </p>
                <ul
                  id="icons03"
                  class="style1 icons"
                  style="opacity: 1; transform: none"
                >
                  <li>
                    <a
                      class="n01"
                      onclick="_nextScrollPoint(event);"
                      role="button"
                      ><svg aria-labelledby="icons03-icon-1-title">
                        <title id="icons03-icon-1-title">Chevron Down</title>
                        <use
                          xlink:href="#icon-ecbd7520f72820fb3c1bfce19d8ed9ce"
                        ></use></svg
                      ><span class="label">Chevron Down</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div
            id="container08"
            data-scroll-id="four"
            data-scroll-behavior="default"
            data-scroll-offset="0"
            data-scroll-speed="2"
            data-scroll-invisible="1"
            class="style1 container default full screen"
            style="
              --element-top: 2781;
              --onvisible-speed: 1s;
              --onvisible-background-color: rgba(0, 0, 0, 0.001);
            "
          >
            <div class="wrapper">
              <div
                class="inner"
                data-onvisible-trigger="1"
                style="opacity: 1; transform-origin: 50% 50%; transform: none"
              >
                <p
                  id="text01"
                  class="style2"
                  style="opacity: 1; transform: none"
                >
                  last man standing
                </p>
                <h2
                  id="text02"
                  class="style1"
                  style="opacity: 1; transform: none"
                >
                  No LUCK in draw?
                </h2>
                <p
                  id="text14"
                  class="style3"
                  style="opacity: 1; transform: none"
                >
                  Don't worry! we still got you. Now you have paid enough to get
                  sponsored for Umrah. Congratulations... now prepare for the
                  blessed Journey. We are taking you to Umrah.
                </p>
                <ul
                  id="buttons03"
                  class="style1 buttons"
                  style="opacity: 1; transform: none"
                >
                  <li>
                    <a
                      href="https://www.yaakhwajatours.com"
                      class="button n01"
                      role="button"
                      ><svg aria-labelledby="buttons03-icon-1-title">
                        <title id="buttons03-icon-1-title">
                          Chevron Right (Light)
                        </title>
                        <use
                          xlink:href="#icon-80dc50be409b6c0a58c1f62335c07e7d"
                        ></use></svg
                      ><span class="label">Book Your Seat Now!</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div id="credits" class="icc-credits">
            <span
              ><a href="https://carrd.co/build?ref=auto"
                >Made with Carrd</a
              ></span
            >
          </div>
        </div>
      </div>
    </div>
    <script>
      (function () {
        var on = addEventListener,
          off = removeEventListener,
          $ = function (q) {
            return document.querySelector(q);
          },
          $$ = function (q) {
            return document.querySelectorAll(q);
          },
          $body = document.body,
          $inner = $(".inner"),
          client = (function () {
            var o = {
                browser: "other",
                browserVersion: 0,
                os: "other",
                osVersion: 0,
                mobile: false,
                canUse: null,
                flags: { lsdUnits: false },
              },
              ua = navigator.userAgent,
              a,
              i;
            a = [
              ["firefox", /Firefox\/([0-9\.]+)/, null],
              ["edge", /Edge\/([0-9\.]+)/, null],
              ["safari", /Version\/([0-9\.]+).+Safari/, null],
              ["chrome", /Chrome\/([0-9\.]+)/, null],
              ["chrome", /CriOS\/([0-9\.]+)/, null],
              ["ie", /Trident\/.+rv:([0-9]+)/, null],
              [
                "safari",
                /iPhone OS ([0-9_]+)/,
                function (v) {
                  return v.replace("_", ".").replace("_", "");
                },
              ],
            ];
            for (i = 0; i < a.length; i++) {
              if (ua.match(a[i][1])) {
                o.browser = a[i][0];
                o.browserVersion = parseFloat(
                  a[i][2] ? a[i][2](RegExp.$1) : RegExp.$1
                );
                break;
              }
            }
            a = [
              [
                "ios",
                /([0-9_]+) like Mac OS X/,
                function (v) {
                  return v.replace("_", ".").replace("_", "");
                },
              ],
              [
                "ios",
                /CPU like Mac OS X/,
                function (v) {
                  return 0;
                },
              ],
              [
                "ios",
                /iPad; CPU/,
                function (v) {
                  return 0;
                },
              ],
              ["android", /Android ([0-9\.]+)/, null],
              [
                "mac",
                /Macintosh.+Mac OS X ([0-9_]+)/,
                function (v) {
                  return v.replace("_", ".").replace("_", "");
                },
              ],
              ["windows", /Windows NT ([0-9\.]+)/, null],
              ["undefined", /Undefined/, null],
            ];
            for (i = 0; i < a.length; i++) {
              if (ua.match(a[i][1])) {
                o.os = a[i][0];
                o.osVersion = parseFloat(
                  a[i][2] ? a[i][2](RegExp.$1) : RegExp.$1
                );
                break;
              }
            }
            if (
              o.os == "mac" &&
              "ontouchstart" in window &&
              ((screen.width == 1024 && screen.height == 1366) ||
                (screen.width == 834 && screen.height == 1112) ||
                (screen.width == 810 && screen.height == 1080) ||
                (screen.width == 768 && screen.height == 1024))
            )
              o.os = "ios";
            o.mobile = o.os == "android" || o.os == "ios";
            var _canUse = document.createElement("div");
            o.canUse = function (property, value) {
              var style;
              style = _canUse.style;
              if (!(property in style)) return false;
              if (typeof value !== "undefined") {
                style[property] = value;
                if (style[property] == "") return false;
              }
              return true;
            };
            o.flags.lsdUnits = o.canUse("width", "100dvw");
            return o;
          })(),
          ready = {
            list: [],
            add: function (f) {
              this.list.push(f);
            },
            run: function () {
              this.list.forEach((f) => {
                f();
              });
            },
          },
          trigger = function (t) {
            dispatchEvent(new Event(t));
          },
          cssRules = function (selectorText) {
            var ss = document.styleSheets,
              a = [],
              f = function (s) {
                var r = s.cssRules,
                  i;
                for (i = 0; i < r.length; i++) {
                  if (
                    r[i] instanceof CSSMediaRule &&
                    matchMedia(r[i].conditionText).matches
                  )
                    f(r[i]);
                  else if (
                    r[i] instanceof CSSStyleRule &&
                    r[i].selectorText == selectorText
                  )
                    a.push(r[i]);
                }
              },
              x,
              i;
            for (i = 0; i < ss.length; i++) f(ss[i]);
            return a;
          },
          escapeHtml = function (s) {
            if (s === "" || s === null || s === undefined) return "";
            var a = {
              "&": "&amp;",
              "<": "&lt;",
              ">": "&gt;",
              '"': "&quot;",
              "'": "&#39;",
            };
            s = s.replace(/[&<>"']/g, function (x) {
              return a[x];
            });
            return s;
          },
          thisHash = function () {
            var h = location.hash ? location.hash.substring(1) : null,
              a;
            if (!h) return null;
            if (h.match(/\?/)) {
              a = h.split("?");
              h = a[0];
              history.replaceState(undefined, undefined, "#" + h);
              window.location.search = a[1];
            }
            if (h.length > 0 && !h.match(/^[a-zA-Z]/)) h = "x" + h;
            if (typeof h == "string") h = h.toLowerCase();
            return h;
          },
          scrollToElement = function (e, style, duration) {
            var y, cy, dy, start, easing, offset, f;
            if (!e) y = 0;
            else {
              offset =
                (e.dataset.scrollOffset
                  ? parseInt(e.dataset.scrollOffset)
                  : 0) *
                parseFloat(getComputedStyle(document.documentElement).fontSize);
              switch (
                e.dataset.scrollBehavior ? e.dataset.scrollBehavior : "default"
              ) {
                case "default":
                default:
                  y = e.offsetTop + offset;
                  break;
                case "center":
                  if (e.offsetHeight < window.innerHeight)
                    y =
                      e.offsetTop -
                      (window.innerHeight - e.offsetHeight) / 2 +
                      offset;
                  else y = e.offsetTop - offset;
                  break;
                case "previous":
                  if (e.previousElementSibling)
                    y =
                      e.previousElementSibling.offsetTop +
                      e.previousElementSibling.offsetHeight +
                      offset;
                  else y = e.offsetTop + offset;
                  break;
              }
            }
            if (!style) style = "smooth";
            if (!duration) duration = 750;
            if (style == "instant") {
              window.scrollTo(0, y);
              return;
            }
            start = Date.now();
            cy = window.scrollY;
            dy = y - cy;
            switch (style) {
              case "linear":
                easing = function (t) {
                  return t;
                };
                break;
              case "smooth":
                easing = function (t) {
                  return t < 0.5
                    ? 4 * t * t * t
                    : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
                };
                break;
            }
            f = function () {
              var t = Date.now() - start;
              if (t >= duration) window.scroll(0, y);
              else {
                window.scroll(0, cy + dy * easing(t / duration));
                requestAnimationFrame(f);
              }
            };
            f();
          },
          scrollToTop = function () {
            scrollToElement(null);
          },
          loadElements = function (parent) {
            var a, e, x, i;
            a = parent.querySelectorAll('iframe[data-src]:not([data-src=""])');
            for (i = 0; i < a.length; i++) {
              a[i].contentWindow.location.replace(a[i].dataset.src);
              a[i].dataset.initialSrc = a[i].dataset.src;
              a[i].dataset.src = "";
            }
            a = parent.querySelectorAll("video[autoplay]");
            for (i = 0; i < a.length; i++) {
              if (a[i].paused) a[i].play();
            }
            e = parent.querySelector('[data-autofocus="1"]');
            x = e ? e.tagName : null;
            switch (x) {
              case "FORM":
                e = e.querySelector(
                  ".field input, .field select, .field textarea"
                );
                if (e) e.focus();
                break;
              default:
                break;
            }
            a = parent.querySelectorAll("unloaded-script");
            for (i = 0; i < a.length; i++) {
              x = document.createElement("script");
              x.setAttribute("data-loaded", "");
              if (a[i].getAttribute("src"))
                x.setAttribute("src", a[i].getAttribute("src"));
              if (a[i].textContent) x.textContent = a[i].textContent;
              a[i].replaceWith(x);
            }
            x = new Event("loadelements");
            a = parent.querySelectorAll("[data-unloaded]");
            a.forEach((element) => {
              element.removeAttribute("data-unloaded");
              element.dispatchEvent(x);
            });
          },
          unloadElements = function (parent) {
            var a, e, x, i;
            a = parent.querySelectorAll('iframe[data-src=""]');
            for (i = 0; i < a.length; i++) {
              if (a[i].dataset.srcUnload === "0") continue;
              if ("initialSrc" in a[i].dataset)
                a[i].dataset.src = a[i].dataset.initialSrc;
              else a[i].dataset.src = a[i].src;
              a[i].contentWindow.location.replace("about:blank");
            }
            a = parent.querySelectorAll("video");
            for (i = 0; i < a.length; i++) {
              if (!a[i].paused) a[i].pause();
            }
            e = $(":focus");
            if (e) e.blur();
          };
        window._scrollToTop = scrollToTop;
        var thisUrl = function () {
          return window.location.href
            .replace(window.location.search, "")
            .replace(/#$/, "");
        };
        var getVar = function (name) {
          var a = window.location.search.substring(1).split("&"),
            b,
            k;
          for (k in a) {
            b = a[k].split("=");
            if (b[0] == name) return b[1];
          }
          return null;
        };
        var errors = {
          handle: function (handler) {
            window.onerror = function (message, url, line, column, error) {
              handler(error.message);
              return true;
            };
          },
          unhandle: function () {
            window.onerror = null;
          },
        };
        var loaderTimeout = setTimeout(function () {
          $body.classList.add("with-loader");
        }, 500);
        var $loaderElement = document.createElement("div");
        $loaderElement.id = "loader";
        $body.appendChild($loaderElement);
        var loadHandler = function () {
          setTimeout(function () {
            clearTimeout(loaderTimeout);
            $body.classList.remove("is-loading");
            $body.classList.add("is-playing");
            setTimeout(function () {
              $body.classList.remove("with-loader");
              $body.classList.remove("is-playing");
              $body.classList.add("is-ready");
              setTimeout(function () {
                $body.removeChild($loaderElement);
              }, 1000);
            }, 1000);
          }, 100);
        };
        on("load", loadHandler);
        loadElements(document.body);
        (function () {
          var scrollPointParent = function (target) {
              var inner;
              inner = $("#main > .inner");
              while (target && target.parentElement != inner)
                target = target.parentElement;
              return target;
            },
            scrollPointSpeed = function (scrollPoint) {
              let x = parseInt(scrollPoint.dataset.scrollSpeed);
              switch (x) {
                case 5:
                  return 250;
                case 4:
                  return 500;
                case 3:
                  return 750;
                case 2:
                  return 1000;
                case 1:
                  return 1250;
                default:
                  break;
              }
              return 750;
            },
            doNextScrollPoint = function (event) {
              var e, target, id;
              e = scrollPointParent(event.target);
              if (!e) return;
              while (e && e.nextElementSibling) {
                e = e.nextElementSibling;
                if (e.dataset.scrollId) {
                  target = e;
                  id = e.dataset.scrollId;
                  break;
                }
              }
              if (!target || !id) return;
              if (target.dataset.scrollInvisible == "1")
                scrollToElement(target, "smooth", scrollPointSpeed(target));
              else location.href = "#" + id;
            },
            doPreviousScrollPoint = function (e) {
              var e, target, id;
              e = scrollPointParent(event.target);
              if (!e) return;
              while (e && e.previousElementSibling) {
                e = e.previousElementSibling;
                if (e.dataset.scrollId) {
                  target = e;
                  id = e.dataset.scrollId;
                  break;
                }
              }
              if (!target || !id) return;
              if (target.dataset.scrollInvisible == "1")
                scrollToElement(target, "smooth", scrollPointSpeed(target));
              else location.href = "#" + id;
            },
            doFirstScrollPoint = function (e) {
              var e, target, id;
              e = scrollPointParent(event.target);
              if (!e) return;
              while (e && e.previousElementSibling) {
                e = e.previousElementSibling;
                if (e.dataset.scrollId) {
                  target = e;
                  id = e.dataset.scrollId;
                }
              }
              if (!target || !id) return;
              if (target.dataset.scrollInvisible == "1")
                scrollToElement(target, "smooth", scrollPointSpeed(target));
              else location.href = "#" + id;
            },
            doLastScrollPoint = function (e) {
              var e, target, id;
              e = scrollPointParent(event.target);
              if (!e) return;
              while (e && e.nextElementSibling) {
                e = e.nextElementSibling;
                if (e.dataset.scrollId) {
                  target = e;
                  id = e.dataset.scrollId;
                }
              }
              if (!target || !id) return;
              if (target.dataset.scrollInvisible == "1")
                scrollToElement(target, "smooth", scrollPointSpeed(target));
              else location.href = "#" + id;
            };
          window._nextScrollPoint = doNextScrollPoint;
          window._previousScrollPoint = doPreviousScrollPoint;
          window._firstScrollPoint = doFirstScrollPoint;
          window._lastScrollPoint = doLastScrollPoint;
          window._scrollToTop = function () {
            scrollToElement(null);
            if (window.location.hash) {
              history.pushState(null, null, ".");
            }
          };
          if ("scrollRestoration" in history)
            history.scrollRestoration = "manual";
          on("load", function () {
            var initialScrollPoint, h;
            h = thisHash();
            if (h && !h.match(/^[a-zA-Z0-9\-]+$/)) h = null;
            initialScrollPoint = $('[data-scroll-id="' + h + '"]');
            if (initialScrollPoint)
              scrollToElement(initialScrollPoint, "instant");
          });
          on("hashchange", function (event) {
            var scrollPoint, h, pos;
            h = thisHash();
            if (h && !h.match(/^[a-zA-Z0-9\-]+$/)) return false;
            scrollPoint = $('[data-scroll-id="' + h + '"]');
            if (scrollPoint)
              scrollToElement(
                scrollPoint,
                "smooth",
                scrollPointSpeed(scrollPoint)
              );
            else scrollToElement(null);
            return false;
          });
          on("click", function (event) {
            var t = event.target,
              tagName = t.tagName.toUpperCase(),
              scrollPoint;
            switch (tagName) {
              case "IMG":
              case "SVG":
              case "USE":
              case "U":
              case "STRONG":
              case "EM":
              case "CODE":
              case "S":
              case "MARK":
              case "SPAN":
                while (!!(t = t.parentElement)) if (t.tagName == "A") break;
                if (!t) return;
                break;
              default:
                break;
            }
            if (
              t.tagName == "A" &&
              t.getAttribute("href") !== null &&
              t.getAttribute("href").substr(0, 1) == "#"
            ) {
              if (
                !!(scrollPoint = $(
                  '[data-scroll-id="' +
                    t.hash.substr(1) +
                    '"][data-scroll-invisible="1"]'
                ))
              ) {
                event.preventDefault();
                scrollToElement(
                  scrollPoint,
                  "smooth",
                  scrollPointSpeed(scrollPoint)
                );
              } else if (t.hash == window.location.hash) {
                event.preventDefault();
                history.replaceState(undefined, undefined, "#");
                location.replace(t.hash);
              }
            }
          });
        })();
        var style, sheet, rule;
        style = document.createElement("style");
        style.appendChild(document.createTextNode(""));
        document.head.appendChild(style);
        sheet = style.sheet;
        if (client.mobile) {
          (function () {
            if (client.flags.lsdUnits) {
              document.documentElement.style.setProperty(
                "--viewport-height",
                "100svh"
              );
              document.documentElement.style.setProperty(
                "--background-height",
                "100lvh"
              );
            } else {
              var f = function () {
                document.documentElement.style.setProperty(
                  "--viewport-height",
                  window.innerHeight + "px"
                );
                document.documentElement.style.setProperty(
                  "--background-height",
                  window.innerHeight + 250 + "px"
                );
              };
              on("load", f);
              on("orientationchange", function () {
                setTimeout(function () {
                  f();
                }, 100);
              });
            }
          })();
        }
        if (client.os == "android") {
          (function () {
            sheet.insertRule("body::after { }", 0);
            rule = sheet.cssRules[0];
            var f = function () {
              rule.style.cssText =
                "height: " + Math.max(screen.width, screen.height) + "px";
            };
            on("load", f);
            on("orientationchange", f);
            on("touchmove", f);
          })();
          $body.classList.add("is-touch");
        } else if (client.os == "ios") {
          if (client.osVersion <= 11)
            (function () {
              sheet.insertRule("body::after { }", 0);
              rule = sheet.cssRules[0];
              rule.style.cssText = "-webkit-transform: scale(1.0)";
            })();
          if (client.osVersion <= 11)
            (function () {
              sheet.insertRule("body.ios-focus-fix::before { }", 0);
              rule = sheet.cssRules[0];
              rule.style.cssText = "height: calc(100% + 60px)";
              on(
                "focus",
                function (event) {
                  $body.classList.add("ios-focus-fix");
                },
                true
              );
              on(
                "blur",
                function (event) {
                  $body.classList.remove("ios-focus-fix");
                },
                true
              );
            })();
          $body.classList.add("is-touch");
        }
        var scrollEvents = {
          items: [],
          add: function (o) {
            this.items.push({
              element: o.element,
              triggerElement:
                "triggerElement" in o && o.triggerElement
                  ? o.triggerElement
                  : o.element,
              enter: "enter" in o ? o.enter : null,
              leave: "leave" in o ? o.leave : null,
              mode: "mode" in o ? o.mode : 4,
              threshold: "threshold" in o ? o.threshold : 0.25,
              offset: "offset" in o ? o.offset : 0,
              initialState: "initialState" in o ? o.initialState : null,
              state: false,
            });
          },
          handler: function () {
            var height, top, bottom, scrollPad;
            if (client.os == "ios") {
              height = document.documentElement.clientHeight;
              top = document.body.scrollTop + window.scrollY;
              bottom = top + height;
              scrollPad = 125;
            } else {
              height = document.documentElement.clientHeight;
              top = document.documentElement.scrollTop;
              bottom = top + height;
              scrollPad = 0;
            }
            scrollEvents.items.forEach(function (item) {
              var elementTop,
                elementBottom,
                viewportTop,
                viewportBottom,
                bcr,
                pad,
                state,
                a,
                b;
              if (!item.enter && !item.leave) return true;
              if (!item.triggerElement) return true;
              if (item.triggerElement.offsetParent === null) {
                if (item.state == true && item.leave) {
                  item.state = false;
                  item.leave.apply(item.element);
                  if (!item.enter) item.leave = null;
                }
                return true;
              }
              bcr = item.triggerElement.getBoundingClientRect();
              elementTop = top + Math.floor(bcr.top);
              elementBottom = elementTop + bcr.height;
              if (item.initialState !== null) {
                state = item.initialState;
                item.initialState = null;
              } else {
                switch (item.mode) {
                  case 1:
                  default:
                    state =
                      bottom > elementTop - item.offset &&
                      top < elementBottom + item.offset;
                    break;
                  case 2:
                    a = top + height * 0.5;
                    state =
                      a > elementTop - item.offset &&
                      a < elementBottom + item.offset;
                    break;
                  case 3:
                    a = top + height * item.threshold;
                    if (a - height * 0.375 <= 0) a = 0;
                    b = top + height * (1 - item.threshold);
                    if (
                      b + height * 0.375 >=
                      document.body.scrollHeight - scrollPad
                    )
                      b = document.body.scrollHeight + scrollPad;
                    state =
                      b > elementTop - item.offset &&
                      a < elementBottom + item.offset;
                    break;
                  case 4:
                    pad = height * item.threshold;
                    viewportTop = top + pad;
                    viewportBottom = bottom - pad;
                    if (Math.floor(top) <= pad) viewportTop = top;
                    if (Math.ceil(bottom) >= document.body.scrollHeight - pad)
                      viewportBottom = bottom;
                    if (
                      viewportBottom - viewportTop >=
                      elementBottom - elementTop
                    ) {
                      state =
                        (elementTop >= viewportTop &&
                          elementBottom <= viewportBottom) ||
                        (elementTop >= viewportTop &&
                          elementTop <= viewportBottom) ||
                        (elementBottom >= viewportTop &&
                          elementBottom <= viewportBottom);
                    } else
                      state =
                        (viewportTop >= elementTop &&
                          viewportBottom <= elementBottom) ||
                        (elementTop >= viewportTop &&
                          elementTop <= viewportBottom) ||
                        (elementBottom >= viewportTop &&
                          elementBottom <= viewportBottom);
                    break;
                }
              }
              if (state != item.state) {
                item.state = state;
                if (item.state) {
                  if (item.enter) {
                    item.enter.apply(item.element);
                    if (!item.leave) item.enter = null;
                  }
                } else {
                  if (item.leave) {
                    item.leave.apply(item.element);
                    if (!item.enter) item.leave = null;
                  }
                }
              }
            });
          },
          init: function () {
            on("load", this.handler);
            on("resize", this.handler);
            on("scroll", this.handler);
            this.handler();
          },
        };
        scrollEvents.init();
        var scrollTracking = {
          elements: [],
          add: function (selector) {
            var _this = this;
            $$(selector).forEach(function (e) {
              _this.elements.push(e);
            });
          },
          resizeHandler: function () {
            this.elements.forEach(function (e) {
              e.style.setProperty("--element-top", e.offsetTop);
            });
          },
          scrollHandler: function () {
            document.documentElement.style.setProperty(
              "--scroll-y",
              window.scrollY
            );
          },
          init: function () {
            var _this = this;
            on("scroll", function () {
              _this.scrollHandler();
            });
            on("load", function () {
              _this.scrollHandler();
            });
            this.scrollHandler();
            on("resize", function () {
              _this.resizeHandler();
            });
            on("load", function () {
              _this.resizeHandler();
            });
            this.resizeHandler();
            let x = new ResizeObserver(function (entries) {
              _this.scrollHandler();
              _this.resizeHandler();
            });
            x.observe($body);
          },
        };
        scrollTracking.init();
        var onvisible = {
          effects: {
            "blur-in": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "filter " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.opacity = 0;
                this.style.filter = "blur(" + 0.25 * intensity + "rem)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.filter = "none";
              },
            },
            "zoom-in": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transform =
                  "scale(" + (1 - (alt ? 0.25 : 0.05) * intensity) + ")";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "zoom-out": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transform =
                  "scale(" + (1 + (alt ? 0.25 : 0.05) * intensity) + ")";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "slide-left": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function () {
                this.style.transform = "translateX(100vw)";
              },
              play: function () {
                this.style.transform = "none";
              },
            },
            "slide-right": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function () {
                this.style.transform = "translateX(-100vw)";
              },
              play: function () {
                this.style.transform = "none";
              },
            },
            "flip-forward": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transformOrigin = "50% 50%";
                this.style.transform =
                  "perspective(1000px) rotateX(" +
                  (alt ? 45 : 15) * intensity +
                  "deg)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "flip-backward": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transformOrigin = "50% 50%";
                this.style.transform =
                  "perspective(1000px) rotateX(" +
                  (alt ? -45 : -15) * intensity +
                  "deg)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "flip-left": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transformOrigin = "50% 50%";
                this.style.transform =
                  "perspective(1000px) rotateY(" +
                  (alt ? 45 : 15) * intensity +
                  "deg)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "flip-right": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transformOrigin = "50% 50%";
                this.style.transform =
                  "perspective(1000px) rotateY(" +
                  (alt ? -45 : -15) * intensity +
                  "deg)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "tilt-left": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transform =
                  "rotate(" + (alt ? 45 : 5) * intensity + "deg)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "tilt-right": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity, alt) {
                this.style.opacity = 0;
                this.style.transform =
                  "rotate(" + (alt ? -45 : -5) * intensity + "deg)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "fade-right": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.opacity = 0;
                this.style.transform =
                  "translateX(" + -1.5 * intensity + "rem)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "fade-left": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.opacity = 0;
                this.style.transform = "translateX(" + 1.5 * intensity + "rem)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "fade-down": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.opacity = 0;
                this.style.transform =
                  "translateY(" + -1.5 * intensity + "rem)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "fade-up": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.opacity = 0;
                this.style.transform = "translateY(" + 1.5 * intensity + "rem)";
              },
              play: function () {
                this.style.opacity = 1;
                this.style.transform = "none";
              },
            },
            "fade-in": {
              type: "transition",
              transition: function (speed, delay) {
                return (
                  "opacity " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function () {
                this.style.opacity = 0;
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "fade-in-background": {
              type: "manual",
              rewind: function () {
                this.style.removeProperty("--onvisible-delay");
                this.style.removeProperty("--onvisible-background-color");
              },
              play: function (speed, delay) {
                this.style.setProperty("--onvisible-speed", speed + "s");
                if (delay)
                  this.style.setProperty("--onvisible-delay", delay + "s");
                this.style.setProperty(
                  "--onvisible-background-color",
                  "rgba(0,0,0,0.001)"
                );
              },
            },
            "zoom-in-image": {
              type: "transition",
              target: "img",
              transition: function (speed, delay) {
                return (
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function () {
                this.style.transform = "scale(1)";
              },
              play: function (intensity) {
                this.style.transform = "scale(" + (1 + 0.1 * intensity) + ")";
              },
            },
            "zoom-out-image": {
              type: "transition",
              target: "img",
              transition: function (speed, delay) {
                return (
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.transform = "scale(" + (1 + 0.1 * intensity) + ")";
              },
              play: function () {
                this.style.transform = "none";
              },
            },
            "focus-image": {
              type: "transition",
              target: "img",
              transition: function (speed, delay) {
                return (
                  "transform " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "") +
                  ", " +
                  "filter " +
                  speed +
                  "s ease" +
                  (delay ? " " + delay + "s" : "")
                );
              },
              rewind: function (intensity) {
                this.style.transform = "scale(" + (1 + 0.05 * intensity) + ")";
                this.style.filter = "blur(" + 0.25 * intensity + "rem)";
              },
              play: function (intensity) {
                this.style.transform = "none";
                this.style.filter = "none";
              },
            },
            "wipe-up": {
              type: "animate",
              keyframes: function (intensity) {
                return [
                  {
                    maskSize: "100% 0%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                  {
                    maskSize: "110% 110%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1, easing: "ease" };
              },
              rewind: function () {
                this.style.opacity = 0;
                this.style.maskComposite = "exclude";
                this.style.maskRepeat = "no-repeat";
                this.style.maskPosition = "0% 100%";
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "wipe-down": {
              type: "animate",
              keyframes: function (intensity) {
                return [
                  {
                    maskSize: "100% 0%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                  {
                    maskSize: "110% 110%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1, easing: "ease" };
              },
              rewind: function () {
                this.style.opacity = 0;
                this.style.maskComposite = "exclude";
                this.style.maskRepeat = "no-repeat";
                this.style.maskPosition = "0% 0%";
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "wipe-left": {
              type: "animate",
              keyframes: function (intensity) {
                return [
                  {
                    maskSize: "0% 100%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                  {
                    maskSize: "110% 110%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1, easing: "ease" };
              },
              rewind: function () {
                this.style.opacity = 0;
                this.style.maskComposite = "exclude";
                this.style.maskRepeat = "no-repeat";
                this.style.maskPosition = "100% 0%";
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "wipe-right": {
              type: "animate",
              keyframes: function (intensity) {
                return [
                  {
                    maskSize: "0% 100%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                  {
                    maskSize: "110% 110%",
                    maskImage:
                      "linear-gradient(90deg, black 100%, transparent 100%)",
                  },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1, easing: "ease" };
              },
              rewind: function () {
                this.style.opacity = 0;
                this.style.maskComposite = "exclude";
                this.style.maskRepeat = "no-repeat";
                this.style.maskPosition = "0% 0%";
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "wipe-diagonal": {
              type: "animate",
              keyframes: function (intensity) {
                return [
                  {
                    maskSize: "0% 0%",
                    maskImage:
                      "linear-gradient(45deg, black 50%, transparent 50%)",
                  },
                  {
                    maskSize: "220% 220%",
                    maskImage:
                      "linear-gradient(45deg, black 50%, transparent 50%)",
                  },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1, easing: "ease" };
              },
              rewind: function () {
                this.style.opacity = 0;
                this.style.maskComposite = "exclude";
                this.style.maskRepeat = "no-repeat";
                this.style.maskPosition = "0% 100%";
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "wipe-reverse-diagonal": {
              type: "animate",
              keyframes: function (intensity) {
                return [
                  {
                    maskSize: "0% 0%",
                    maskImage:
                      "linear-gradient(135deg, transparent 50%, black 50%)",
                  },
                  {
                    maskSize: "220% 220%",
                    maskImage:
                      "linear-gradient(135deg, transparent 50%, black 50%)",
                  },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1, easing: "ease" };
              },
              rewind: function () {
                this.style.opacity = 0;
                this.style.maskComposite = "exclude";
                this.style.maskRepeat = "no-repeat";
                this.style.maskPosition = "100% 100%";
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "pop-in": {
              type: "animate",
              keyframes: function (intensity) {
                let diff = (intensity + 1) * 0.025;
                return [
                  { opacity: 0, transform: "scale(" + (1 - diff) + ")" },
                  { opacity: 1, transform: "scale(" + (1 + diff) + ")" },
                  {
                    opacity: 1,
                    transform: "scale(" + (1 - diff * 0.25) + ")",
                    offset: 0.9,
                  },
                  { opacity: 1, transform: "scale(1)" },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1 };
              },
              rewind: function () {
                this.style.opacity = 0;
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "bounce-up": {
              type: "animate",
              keyframes: function (intensity) {
                let diff = (intensity + 1) * 0.075;
                return [
                  { opacity: 0, transform: "translateY(" + diff + "rem)" },
                  { opacity: 1, transform: "translateY(" + -1 * diff + "rem)" },
                  {
                    opacity: 1,
                    transform: "translateY(" + diff * 0.25 + "rem)",
                    offset: 0.9,
                  },
                  { opacity: 1, transform: "translateY(0)" },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1 };
              },
              rewind: function () {
                this.style.opacity = 0;
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "bounce-down": {
              type: "animate",
              keyframes: function (intensity) {
                let diff = (intensity + 1) * 0.075;
                return [
                  { opacity: 0, transform: "translateY(" + -1 * diff + "rem)" },
                  { opacity: 1, transform: "translateY(" + diff + "rem)" },
                  {
                    opacity: 1,
                    transform: "translateY(" + -1 * (diff * 0.25) + "rem)",
                    offset: 0.9,
                  },
                  { opacity: 1, transform: "translateY(0)" },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1 };
              },
              rewind: function () {
                this.style.opacity = 0;
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "bounce-left": {
              type: "animate",
              keyframes: function (intensity) {
                let diff = (intensity + 1) * 0.075;
                return [
                  { opacity: 0, transform: "translateX(" + diff + "rem)" },
                  { opacity: 1, transform: "translateX(" + -1 * diff + "rem)" },
                  {
                    opacity: 1,
                    transform: "translateX(" + diff * 0.25 + "rem)",
                    offset: 0.9,
                  },
                  { opacity: 1, transform: "translateX(0)" },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1 };
              },
              rewind: function () {
                this.style.opacity = 0;
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
            "bounce-right": {
              type: "animate",
              keyframes: function (intensity) {
                let diff = (intensity + 1) * 0.075;
                return [
                  { opacity: 0, transform: "translateX(" + -1 * diff + "rem)" },
                  { opacity: 1, transform: "translateX(" + diff + "rem)" },
                  {
                    opacity: 1,
                    transform: "translateX(" + -1 * (diff * 0.25) + "rem)",
                    offset: 0.9,
                  },
                  { opacity: 1, transform: "translateX(0)" },
                ];
              },
              options: function (speed) {
                return { duration: speed, iterations: 1 };
              },
              rewind: function () {
                this.style.opacity = 0;
              },
              play: function () {
                this.style.opacity = 1;
              },
            },
          },
          regex: new RegExp("([^\\s]+)", "g"),
          add: function (selector, settings) {
            var _this = this,
              style = settings.style in this.effects ? settings.style : "fade",
              speed = parseInt("speed" in settings ? settings.speed : 0),
              intensity = parseInt(
                "intensity" in settings ? settings.intensity : 5
              ),
              delay = parseInt("delay" in settings ? settings.delay : 0),
              replay = "replay" in settings ? settings.replay : false,
              stagger =
                "stagger" in settings
                  ? parseInt(settings.stagger) >= 0
                    ? parseInt(settings.stagger)
                    : false
                  : false,
              staggerOrder =
                "staggerOrder" in settings ? settings.staggerOrder : "default",
              staggerSelector =
                "staggerSelector" in settings ? settings.staggerSelector : null,
              threshold = parseInt(
                "threshold" in settings ? settings.threshold : 3
              ),
              state = "state" in settings ? settings.state : null,
              effect = this.effects[style],
              enter,
              leave,
              scrollEventThreshold;
            if (window.CARRD_DISABLE_ANIMATION === true) {
              if (style == "fade-in-background")
                $$(selector).forEach(function (e) {
                  e.style.setProperty(
                    "--onvisible-background-color",
                    "rgba(0,0,0,0.001)"
                  );
                });
              return;
            }
            switch (threshold) {
              case 1:
                scrollEventThreshold = 0;
                break;
              case 2:
                scrollEventThreshold = 0.125;
                break;
              default:
              case 3:
                scrollEventThreshold = 0.25;
                break;
              case 4:
                scrollEventThreshold = 0.375;
                break;
              case 5:
                scrollEventThreshold = 0.475;
                break;
            }
            switch (effect.type) {
              default:
              case "transition":
                intensity = (intensity / 10) * 1.75 + 0.25;
                enter = function (children, staggerDelay = 0) {
                  var _this = this,
                    transitionOrig;
                  if (effect.target) _this = this.querySelector(effect.target);
                  transitionOrig = _this.style.transition;
                  _this.style.setProperty("backface-visibility", "hidden");
                  _this.style.transition = effect.transition.apply(_this, [
                    speed / 1000,
                    (delay + staggerDelay) / 1000,
                  ]);
                  effect.play.apply(_this, [intensity, !!children]);
                  setTimeout(function () {
                    _this.style.removeProperty("backface-visibility");
                    _this.style.transition = transitionOrig;
                  }, (speed + delay + staggerDelay) * 2);
                };
                leave = function (children) {
                  var _this = this,
                    transitionOrig;
                  if (effect.target) _this = this.querySelector(effect.target);
                  transitionOrig = _this.style.transition;
                  _this.style.setProperty("backface-visibility", "hidden");
                  _this.style.transition = effect.transition.apply(_this, [
                    speed / 1000,
                  ]);
                  effect.rewind.apply(_this, [intensity, !!children]);
                  setTimeout(function () {
                    _this.style.removeProperty("backface-visibility");
                    _this.style.transition = transitionOrig;
                  }, speed * 2);
                };
                break;
              case "animate":
                enter = function (children, staggerDelay = 0) {
                  var _this = this,
                    transitionOrig;
                  if (effect.target) _this = this.querySelector(effect.target);
                  setTimeout(() => {
                    effect.play.apply(_this, []);
                    _this.animate(
                      effect.keyframes.apply(_this, [intensity]),
                      effect.options.apply(_this, [speed, delay])
                    );
                  }, delay + staggerDelay);
                };
                leave = function (children) {
                  var _this = this,
                    transitionOrig;
                  if (effect.target) _this = this.querySelector(effect.target);
                  let a = _this.animate(
                    effect.keyframes.apply(_this, [intensity]),
                    effect.options.apply(_this, [speed, delay])
                  );
                  a.reverse();
                  a.addEventListener("finish", () => {
                    effect.rewind.apply(_this, []);
                  });
                };
                break;
              case "manual":
                enter = function (children, staggerDelay = 0) {
                  var _this = this,
                    transitionOrig;
                  if (effect.target) _this = this.querySelector(effect.target);
                  effect.play.apply(_this, [
                    speed / 1000,
                    (delay + staggerDelay) / 1000,
                    intensity,
                  ]);
                };
                leave = function (children) {
                  var _this = this,
                    transitionOrig;
                  if (effect.target) _this = this.querySelector(effect.target);
                  effect.rewind.apply(_this, [intensity, !!children]);
                };
                break;
            }
            $$(selector).forEach(function (e) {
              var children, targetElement, triggerElement;
              if (stagger !== false && staggerSelector == ":scope > *")
                _this.expandTextNodes(e);
              children =
                stagger !== false && staggerSelector
                  ? e.querySelectorAll(staggerSelector)
                  : null;
              if (effect.target) targetElement = e.querySelector(effect.target);
              else targetElement = e;
              if (children)
                children.forEach(function (targetElement) {
                  effect.rewind.apply(targetElement, [intensity, true]);
                });
              else effect.rewind.apply(targetElement, [intensity]);
              triggerElement = e;
              if (e.parentNode) {
                if (e.parentNode.dataset.onvisibleTrigger)
                  triggerElement = e.parentNode;
                else if (e.parentNode.parentNode) {
                  if (e.parentNode.parentNode.dataset.onvisibleTrigger)
                    triggerElement = e.parentNode.parentNode;
                }
              }
              scrollEvents.add({
                element: e,
                triggerElement: triggerElement,
                initialState: state,
                threshold: scrollEventThreshold,
                enter: children
                  ? function () {
                      var staggerDelay = 0,
                        childHandler = function (e) {
                          enter.apply(e, [children, staggerDelay]);
                          staggerDelay += stagger;
                        },
                        a;
                      if (staggerOrder == "default") {
                        children.forEach(childHandler);
                      } else {
                        a = Array.from(children);
                        switch (staggerOrder) {
                          case "reverse":
                            a.reverse();
                            break;
                          case "random":
                            a.sort(function () {
                              return Math.random() - 0.5;
                            });
                            break;
                        }
                        a.forEach(childHandler);
                      }
                    }
                  : enter,
                leave: replay
                  ? children
                    ? function () {
                        children.forEach(function (e) {
                          leave.apply(e, [children]);
                        });
                      }
                    : leave
                  : null,
              });
            });
          },
          expandTextNodes: function (e) {
            var s, i, w, x;
            for (i = 0; i < e.childNodes.length; i++) {
              x = e.childNodes[i];
              if (x.nodeType != Node.TEXT_NODE) continue;
              s = x.nodeValue;
              s = s.replace(this.regex, function (x, a) {
                return "<text-node>" + escapeHtml(a) + "</text-node>";
              });
              w = document.createElement("text-node");
              w.innerHTML = s;
              x.replaceWith(w);
              while (w.childNodes.length > 0) {
                w.parentNode.insertBefore(w.childNodes[0], w);
              }
              w.parentNode.removeChild(w);
            }
          },
        };
        scrollTracking.add(".container.style1");
        onvisible.add("h1.style2, h2.style2, h3.style2, p.style2", {
          style: "fade-left",
          speed: 1000,
          intensity: 0,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add("h1.style1, h2.style1, h3.style1, p.style1", {
          style: "fade-left",
          speed: 1000,
          intensity: 2,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add("h1.style3, h2.style3, h3.style3, p.style3", {
          style: "fade-left",
          speed: 1000,
          intensity: 4,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add(".buttons.style1", {
          style: "fade-up",
          speed: 1000,
          intensity: 1,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add(".icons.style1", {
          style: "fade-up",
          speed: 1000,
          intensity: 1,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add(".container.style1", {
          style: "fade-in-background",
          speed: 1000,
          intensity: 5,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add(".container.style1 > .wrapper > .inner", {
          style: "flip-left",
          speed: 1000,
          intensity: 0,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        onvisible.add(".container.style2 > .wrapper > .inner", {
          style: "flip-left",
          speed: 1000,
          intensity: 0,
          threshold: 3,
          delay: 0,
          replay: false,
        });
        ready.run();
      })();

      // Custom Carrd-style enhancements
      (function () {
        // Loading overlay
        const loadingOverlay = document.getElementById("loadingOverlay");

        // Hide loading overlay after page loads
        window.addEventListener("load", function () {
          setTimeout(() => {
            loadingOverlay.classList.add("hidden");
            setTimeout(() => {
              loadingOverlay.style.display = "none";
            }, 500);
          }, 1000);
        });

        // Create particle effect
        function createParticles() {
          const particlesContainer = document.getElementById("particles");
          const particleCount = 20;

          for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement("div");
            particle.className = "particle";
            particle.style.left = Math.random() * 100 + "%";
            particle.style.animationDelay = Math.random() * 6 + "s";
            particle.style.animationDuration = Math.random() * 3 + 3 + "s";
            particlesContainer.appendChild(particle);
          }
        }

        // Initialize particles
        createParticles();

        // Add smooth scroll behavior for better UX
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute("href"));
            if (target) {
              target.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
            }
          });
        });

        // Add intersection observer for enhanced animations
        const observerOptions = {
          threshold: 0.1,
          rootMargin: "0px 0px -50px 0px",
        };

        const observer = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.style.opacity = "1";
              entry.target.style.transform = "translateY(0)";
            }
          });
        }, observerOptions);

        // Observe all containers for enhanced animations
        document.querySelectorAll(".container").forEach((container) => {
          observer.observe(container);
        });

        // Add dynamic background color changes on scroll
        let ticking = false;
        function updateBackground() {
          const scrolled = window.pageYOffset;
          const rate = scrolled * -0.5;
          document.body.style.transform = `translateY(${rate}px)`;
          ticking = false;
        }

        function requestTick() {
          if (!ticking) {
            requestAnimationFrame(updateBackground);
            ticking = true;
          }
        }

        window.addEventListener("scroll", requestTick);
      })();
    </script>
  </body>
</html>
