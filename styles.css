* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
  overflow-x: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  scroll-behavior: smooth;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Animated Background */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.1;
}

.floating-shape {
  position: absolute;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}
.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}
.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}
.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Scroll Progress Bar */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
  z-index: 1000;
  transition: width 0.3s ease;
  box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

/* Header */
header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.logo {
  font-size: 1.8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #2d3748;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-2px);
}

.nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

/* Hero Section */
.hero {
  min-height: 90vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  margin: 2rem 0;
  border-radius: 30px;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #2d3748, #4a5568);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-text h2 {
  font-size: 1.3rem;
  color: #666;
  margin-bottom: 2rem;
  font-weight: 400;
}

/* Typing Animation */
.typing-text {
  color: #667eea;
  font-weight: 600;
  position: relative;
}

.typing-text::after {
  content: "|";
  animation: blink 1s infinite;
  color: #ff6b6b;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Hero Stats */
.hero-stats {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.cta-button {
  display: inline-block;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 18px 40px;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
  position: relative;
  overflow: hidden;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.cta-button:hover::before {
  left: 100%;
}

/* Pulse Button Animation */
.pulse-button {
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
  }
  50% {
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.8);
    transform: translateY(-2px);
  }
  100% {
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
  }
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.ai-visual {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: pulse 4s ease-in-out infinite;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.ai-visual::before {
  content: "🤖";
  font-size: 8rem;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Problem Section */
.problem-section {
  background: #1a1a2e;
  color: white;
  padding: 6rem 0;
  margin: 4rem 0;
  border-radius: 30px;
  position: relative;
  overflow: hidden;
}

.problem-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #16213e, #0f3460);
  opacity: 0.8;
}

.problem-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.problem-content h2 {
  font-size: 2.8rem;
  margin-bottom: 3rem;
  font-weight: 800;
}

.problem-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.problem-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.problem-card:hover {
  transform: translateY(-10px);
}

.problem-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #ff6b6b;
}

/* Solution Section */
.solution-section {
  background: white;
  padding: 6rem 0;
  margin: 4rem 0;
  border-radius: 30px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.solution-content {
  text-align: center;
}

.solution-content h2 {
  font-size: 2.8rem;
  margin-bottom: 3rem;
  font-weight: 800;
  color: #2d3748;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.benefit-card {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: left;
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.benefit-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: rotate(45deg);
  transition: all 0.5s;
}

.benefit-card:hover {
  transform: translateY(-10px);
}

.benefit-card:hover::before {
  animation: shine 1s ease-in-out;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.benefit-card .icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.benefit-card h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

/* Offer Section */
.offer-section {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 6rem 0;
  margin: 4rem 0;
  border-radius: 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.offer-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

.offer-content {
  position: relative;
  z-index: 2;
}

.offer-content h2 {
  font-size: 2.8rem;
  margin-bottom: 2rem;
  font-weight: 800;
}

.offer-content .highlight {
  font-size: 1.5rem;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  border-radius: 15px;
  display: inline-block;
  margin: 2rem 0;
}

/* Tools Section */
.tools-section {
  background: white;
  padding: 6rem 0;
  margin: 4rem 0;
  border-radius: 30px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.tools-content h2 {
  text-align: center;
  font-size: 2.8rem;
  margin-bottom: 3rem;
  font-weight: 800;
  color: #2d3748;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.tool-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 2rem;
  border-radius: 20px;
  border-left: 5px solid;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tool-card:nth-child(1) {
  border-left-color: #667eea;
}
.tool-card:nth-child(2) {
  border-left-color: #764ba2;
}
.tool-card:nth-child(3) {
  border-left-color: #ff6b6b;
}
.tool-card:nth-child(4) {
  border-left-color: #4ecdc4;
}

.tool-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.tool-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #2d3748;
  font-weight: 700;
}

.tool-card p {
  color: #666;
  margin-bottom: 1rem;
}

.commission-badge {
  background: linear-gradient(135deg, #10ac84, #00d2d3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  display: inline-block;
}

/* Final CTA Section */
.final-cta {
  background: linear-gradient(135deg, #2d3748, #4a5568);
  color: white;
  padding: 6rem 0;
  margin: 4rem 0;
  border-radius: 30px;
  text-align: center;
  position: relative;
}

.final-cta h2 {
  font-size: 2.8rem;
  margin-bottom: 2rem;
  font-weight: 800;
}

.final-cta p {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
  }

  .ai-visual {
    width: 300px;
    height: 300px;
  }

  .ai-visual::before {
    font-size: 6rem;
  }

  .problem-content h2,
  .solution-content h2,
  .offer-content h2,
  .tools-content h2,
  .testimonials-content h2,
  .final-cta h2 {
    font-size: 2rem;
  }

  .problem-grid,
  .benefits-grid,
  .tools-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .testimonial-card {
    padding: 1.5rem;
  }

  .container {
    padding: 0 15px;
  }

  .cta-button {
    padding: 15px 30px;
    font-size: 1rem;
  }

  /* Touch-friendly interactions */
  .cta-button:hover {
    transform: none;
  }

  .cta-button:active {
    transform: scale(0.98);
  }

  .nav-link:hover {
    transform: none;
  }

  .problem-card:hover,
  .benefit-card:hover,
  .tool-card:hover,
  .testimonial-card:hover {
    transform: none;
  }

  .problem-card:active,
  .benefit-card:active,
  .tool-card:active,
  .testimonial-card:active {
    transform: scale(0.98);
  }
}

@media (max-width: 480px) {
  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-text h2 {
    font-size: 1.1rem;
  }

  .ai-visual {
    width: 250px;
    height: 250px;
  }

  .ai-visual::before {
    font-size: 4rem;
  }

  .hero-stats {
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .problem-content h2,
  .solution-content h2,
  .offer-content h2,
  .tools-content h2,
  .testimonials-content h2,
  .final-cta h2 {
    font-size: 1.8rem;
  }
}

/* Scroll Animation */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Testimonials Section */
.testimonials-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 6rem 0;
  margin: 4rem 0;
  border-radius: 30px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.testimonials-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(255, 107, 107, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.testimonials-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.testimonials-content h2 {
  font-size: 2.8rem;
  margin-bottom: 3rem;
  font-weight: 800;
  color: #2d3748;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 2rem;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.testimonial-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #ff6b6b);
}

.testimonial-content {
  position: relative;
  z-index: 2;
}

.stars {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #ffd700;
}

.testimonial-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.author-info h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.author-info span {
  font-size: 0.9rem;
  color: #666;
}
