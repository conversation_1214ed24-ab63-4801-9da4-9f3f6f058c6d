#!/usr/bin/env python3
"""
AI-Powered Video Creator Script
Combines video from Pexels, music from Pixabay, and generates captions with Gemini AI
"""

import os
import requests
import json
from moviepy.editor import *
from moviepy.video.tools.subtitles import SubtitlesClip
import tempfile
from pathlib import Path
import google.generativeai as genai

class AIVideoCreator:
    def __init__(self):
        # API Keys
        self.pexels_api_key = "Y42fCOJyjmNn88IP6nGhBmidAX2J8EiyMpkk7vse4IjB3YsILyAoBEhw"
        self.pixabay_api_key = "**********************************"
        self.gemini_api_key = "AIzaSyAkelKzPAm0x36YBoMhqgWGt0lPWOYxwDk"
        
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        self.temp_dir = tempfile.mkdtemp()
        
    def generate_content_idea(self, topic):
        """Use Gemini to generate video content ideas"""
        prompt = f"""
        Create a creative video concept for the topic: {topic}
        
        Please provide:
        1. A compelling video title
        2. A brief description (2-3 sentences)
        3. Suggested video search keywords for Pexels
        4. Suggested background music mood/style for Pixabay
        5. 4-6 engaging subtitle lines with timing (each 3-4 seconds long)
        
        Format as JSON:
        {{
            "title": "...",
            "description": "...",
            "video_keywords": "...",
            "music_keywords": "...",
            "subtitles": [
                {{"start": 0, "end": 3, "text": "..."}},
                {{"start": 3, "end": 6, "text": "..."}},
                ...
            ]
        }}
        """
        
        try:
            response = self.model.generate_content(prompt)
            # Clean up the response to extract JSON
            content = response.text.strip()
            if content.startswith('```json'):
                content = content[7:-3]
            elif content.startswith('```'):
                content = content[3:-3]
            
            return json.loads(content)
        except Exception as e:
            print(f"⚠️  Gemini API error: {e}")
            # Fallback content
            return {
                "title": f"Amazing {topic} Video",
                "description": f"A beautiful video about {topic}",
                "video_keywords": topic,
                "music_keywords": "upbeat background",
                "subtitles": [
                    {"start": 0, "end": 3, "text": f"Welcome to {topic}"},
                    {"start": 3, "end": 6, "text": "This is amazing content"},
                    {"start": 6, "end": 9, "text": "Created with AI"},
                    {"start": 9, "end": 12, "text": "Enjoy the experience!"}
                ]
            }
    
    def generate_captions_for_video(self, video_description, duration):
        """Generate contextual captions using Gemini"""
        prompt = f"""
        Create engaging subtitle captions for a {duration:.1f} second video about: {video_description}
        
        Requirements:
        - Each subtitle should be 3-4 seconds long
        - Text should be engaging and descriptive
        - Maximum 10 words per subtitle for readability
        - Cover the entire video duration
        
        Format as JSON array:
        [
            {{"start": 0, "end": 3, "text": "..."}},
            {{"start": 3, "end": 6, "text": "..."}},
            ...
        ]
        """
        
        try:
            response = self.model.generate_content(prompt)
            content = response.text.strip()
            if content.startswith('```json'):
                content = content[7:-3]
            elif content.startswith('```'):
                content = content[3:-3]
            
            return json.loads(content)
        except Exception as e:
            print(f"⚠️  Caption generation error: {e}")
            # Generate simple timed captions
            num_segments = max(1, int(duration / 3))
            return [
                {"start": i*3, "end": min((i+1)*3, duration), "text": f"Segment {i+1}"}
                for i in range(num_segments)
            ]
    
    def search_pexels_video(self, query, per_page=10):
        """Search for videos on Pexels"""
        url = "https://api.pexels.com/videos/search"
        headers = {"Authorization": self.pexels_api_key}
        params = {
            "query": query,
            "per_page": per_page,
            "orientation": "landscape",
            "size": "medium"
        }
        
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Pexels API error: {response.status_code} - {response.text}")
    
    def search_pixabay_music(self, query, per_page=10):
        """Search for music on Pixabay"""
        url = "https://pixabay.com/api/"
        params = {
            "key": self.pixabay_api_key,
            "q": query,
            "audio_type": "music",
            "per_page": per_page,
            "safesearch": "true"
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Pixabay API error: {response.status_code} - {response.text}")
    
    def download_file(self, url, filename):
        """Download a file from URL"""
        filepath = os.path.join(self.temp_dir, filename)
        print(f"📥 Downloading: {filename}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, stream=True, headers=headers)
        response.raise_for_status()
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        return filepath
    
    def create_subtitle_file(self, subtitles, filename="subtitles.srt"):
        """Create an SRT subtitle file"""
        filepath = os.path.join(self.temp_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitles, 1):
                start_time = self.format_time(subtitle['start'])
                end_time = self.format_time(subtitle['end'])
                text = subtitle['text']
                
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")
        
        return filepath
    
    def format_time(self, seconds):
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def create_ai_video(self, topic, custom_subtitles=None, output_filename=None):
        """Create a complete AI-generated video"""
        
        print(f"🤖 Generating content ideas for: {topic}")
        content = self.generate_content_idea(topic)
        
        print(f"📋 Generated concept: {content['title']}")
        print(f"📝 Description: {content['description']}")
        
        # Use custom subtitles or generated ones
        subtitles = custom_subtitles if custom_subtitles else content['subtitles']
        
        if not output_filename:
            safe_title = "".join(c for c in content['title'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{safe_title.replace(' ', '_')}.mp4"
        
        return self.create_video(
            video_query=content['video_keywords'],
            music_query=content['music_keywords'],
            subtitles=subtitles,
            output_filename=output_filename,
            title=content['title']
        )
    
    def create_video(self, video_query, music_query, subtitles, output_filename="final_video.mp4", title="AI Generated Video"):
        """Create the complete video with music and subtitles"""
        
        print(f"🎬 Searching for video: '{video_query}'")
        video_results = self.search_pexels_video(video_query)
        
        if not video_results['videos']:
            raise Exception(f"No videos found for query: {video_query}")
        
        # Get the first suitable video
        video_data = video_results['videos'][0]
        video_url = None
        
        # Find best quality video file
        for file in video_data['video_files']:
            if file['quality'] in ['hd', 'sd']:
                video_url = file['link']
                break
        
        if not video_url:
            video_url = video_data['video_files'][0]['link']
        
        print(f"📥 Downloading video: {video_data['id']} ({video_data.get('duration', 'unknown')}s)")
        video_path = self.download_file(video_url, f"video_{video_data['id']}.mp4")
        
        print(f"🎵 Searching for music: '{music_query}'")
        
        try:
            music_results = self.search_pixabay_music(music_query)
        except Exception as e:
            print(f"⚠️  Music search failed: {e}")
            music_results = {"hits": []}
        
        music_path = None
        if music_results and music_results.get('hits'):
            # Try different approaches to find downloadable audio
            for hit in music_results['hits']:
                print(f"🎵 Checking: {hit.get('tags', 'Unknown track')}")
                
                # Try multiple possible audio URL fields
                audio_url = None
                for url_field in ['webformatURL', 'fullHDURL', 'largeImageURL', 'previewURL']:
                    if url_field in hit and hit[url_field]:
                        if any(ext in hit[url_field].lower() for ext in ['.mp3', '.wav', '.ogg', '.m4a']):
                            audio_url = hit[url_field]
                            break
                
                if audio_url:
                    try:
                        music_path = self.download_file(audio_url, f"music_{hit['id']}.mp3")
                        print(f"✅ Music downloaded: {hit.get('tags', 'Unknown')}")
                        break
                    except Exception as e:
                        print(f"⚠️  Failed to download music: {e}")
                        continue
        
        if not music_path:
            print("⚠️  No suitable music found - creating video without background music")
            print("💡 Tip: You can manually add music later or use local audio files")
        
        print("📝 Creating subtitle file...")
        subtitle_path = self.create_subtitle_file(subtitles)
        
        print("🎬 Combining video components...")
        
        # Load video
        video = VideoFileClip(video_path)
        
        # If we have custom subtitles, adjust video duration to match
        if subtitles:
            max_subtitle_time = max(sub['end'] for sub in subtitles)
            if video.duration < max_subtitle_time:
                # Loop video to match subtitle duration
                loops_needed = int(max_subtitle_time / video.duration) + 1
                video = concatenate_videoclips([video] * loops_needed).subclip(0, max_subtitle_time)
            else:
                # Trim video to reasonable length based on subtitles
                video = video.subclip(0, min(video.duration, max_subtitle_time + 2))
        
        # Add music if available
        if music_path and os.path.exists(music_path):
            try:
                music = AudioFileClip(music_path)
                
                # Adjust music duration to match video
                if music.duration > video.duration:
                    music = music.subclip(0, video.duration)
                elif music.duration < video.duration:
                    loops_needed = int(video.duration / music.duration) + 1
                    music = concatenate_audioclips([music] * loops_needed).subclip(0, video.duration)
                
                # Set music volume
                music = music.volumex(0.4)
                
                # Combine with original audio or use music only
                if video.audio:
                    final_audio = CompositeAudioClip([video.audio.volumex(0.6), music])
                else:
                    final_audio = music
                
                video = video.set_audio(final_audio)
                print("✅ Music added successfully")
            except Exception as e:
                print(f"⚠️  Error adding music: {e}")
        
        # Add subtitles with PIL/Pillow fallback (no ImageMagick needed)
        try:
            # Method 1: Try simple text overlay without ImageMagick
            subtitle_clips = []
            
            for sub in subtitles:
                # Create simple text overlay
                txt_clip = TextClip(sub['text'],
                                  fontsize=48,
                                  color='white',
                                  font='Arial',  # Use system font
                                  stroke_color='black',
                                  stroke_width=2) \
                          .set_start(sub['start']) \
                          .set_duration(sub['end'] - sub['start']) \
                          .set_position(('center', video.h - 100))  # Bottom center
                
                subtitle_clips.append(txt_clip)
            
            if subtitle_clips:
                final_video = CompositeVideoClip([video] + subtitle_clips)
                print("✅ Subtitles added successfully (simple method)")
            else:
                final_video = video
                
        except Exception as e:
            print(f"⚠️  Subtitle error: {e}")
            print("📹 Creating video without subtitles")
            
            # Save subtitles as separate SRT file for manual use
            try:
                srt_path = os.path.join(os.getcwd(), output_filename.replace('.mp4', '.srt'))
                with open(srt_path, 'w', encoding='utf-8') as f:
                    for i, sub in enumerate(subtitles, 1):
                        start_time = self.format_time(sub['start'])
                        end_time = self.format_time(sub['end'])
                        f.write(f"{i}\n{start_time} --> {end_time}\n{sub['text']}\n\n")
                print(f"💾 Subtitles saved separately: {srt_path}")
            except:
                pass
            
            final_video = video
        
        # Export final video
        output_path = os.path.join(os.getcwd(), output_filename)
        print(f"🎬 Rendering final video: {output_filename}")
        print(f"📊 Video stats: {final_video.duration:.1f}s, {final_video.size}")
        
        final_video.write_videofile(
            output_path,
            audio_codec='aac',
            codec='libx264',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # Cleanup clips
        video.close()
        if 'final_audio' in locals():
            final_audio.close()
        final_video.close()
        
        print(f"✅ Video created successfully!")
        print(f"📁 Output: {output_path}")
        print(f"🎯 Title: {title}")
        
        return output_path
    
    def generate_content_idea(self, topic):
        """Use Gemini to generate video content ideas"""
        prompt = f"""
        Create a creative and engaging video concept for: {topic}
        
        Make it appealing and suitable for social media. Provide:
        1. A catchy, clickable title
        2. Brief engaging description 
        3. Search keywords for finding relevant video footage
        4. Music style that fits the mood
        5. 4-6 compelling subtitle captions (each 3-4 seconds, max 8 words each)
        
        Return ONLY valid JSON in this exact format:
        {{
            "title": "Your catchy title here",
            "description": "Brief description here", 
            "video_keywords": "search terms for video",
            "music_keywords": "music style keywords",
            "subtitles": [
                {{"start": 0, "end": 3, "text": "Opening caption"}},
                {{"start": 3, "end": 6, "text": "Second caption"}},
                {{"start": 6, "end": 9, "text": "Third caption"}},
                {{"start": 9, "end": 12, "text": "Closing caption"}}
            ]
        }}
        """
        
        try:
            response = self.model.generate_content(prompt)
            content = response.text.strip()
            
            # Clean up response
            if '```json' in content:
                content = content.split('```json')[1].split('```')[0]
            elif '```' in content:
                content = content.split('```')[1]
            
            return json.loads(content)
        except Exception as e:
            print(f"⚠️  Gemini generation error: {e}")
            return self._fallback_content(topic)
    
    def _fallback_content(self, topic):
        """Fallback content if Gemini fails"""
        return {
            "title": f"Amazing {topic.title()} Experience",
            "description": f"A stunning visual journey through {topic}",
            "video_keywords": topic,
            "music_keywords": "cinematic background",
            "subtitles": [
                {"start": 0, "end": 3, "text": f"Discover {topic}"},
                {"start": 3, "end": 6, "text": "Beauty in motion"},
                {"start": 6, "end": 9, "text": "Created with AI"},
                {"start": 9, "end": 12, "text": "Experience the magic"}
            ]
        }
    
    def search_pexels_video(self, query, per_page=10):
        """Search for videos on Pexels"""
        url = "https://api.pexels.com/videos/search"
        headers = {"Authorization": self.pexels_api_key}
        params = {
            "query": query,
            "per_page": per_page,
            "orientation": "landscape",
            "size": "medium"
        }
        
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Pexels API error: {response.status_code}")
    
    def search_pixabay_music(self, query, per_page=5):
        """Search for music on Pixabay"""
        # Note: Pixabay's audio search is limited and often doesn't return actual audio files
        # This is a known limitation of their free API
        print(f"🎵 Attempting Pixabay music search (limited free API)...")
        
        url = "https://pixabay.com/api/"
        simple_query = "music"  # Use generic term as specific searches often fail
        
        params = {
            "key": self.pixabay_api_key,
            "q": simple_query,
            "audio_type": "music", 
            "per_page": per_page,
            "safesearch": "true"
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"🎵 Pixabay returned {len(data.get('hits', []))} results")
                # Pixabay free tier often doesn't include actual audio downloads
                return {"hits": []}  # Skip for now due to API limitations
            else:
                print(f"⚠️  Pixabay API error {response.status_code}")
                return {"hits": []}
        except Exception as e:
            print(f"⚠️  Pixabay request failed: {e}")
            return {"hits": []}
    
    def download_file(self, url, filename):
        """Download a file from URL"""
        filepath = os.path.join(self.temp_dir, filename)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, stream=True, headers=headers)
        response.raise_for_status()
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        return filepath
    
    def create_subtitle_file(self, subtitles, filename="subtitles.srt"):
        """Create an SRT subtitle file"""
        filepath = os.path.join(self.temp_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitles, 1):
                start_time = self.format_time(subtitle['start'])
                end_time = self.format_time(subtitle['end'])
                text = subtitle['text']
                
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")
        
        return filepath
    
    def format_time(self, seconds):
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def add_simple_local_music(self, video, music_file_path):
        """Add local music file to video"""
        try:
            if os.path.exists(music_file_path):
                music = AudioFileClip(music_file_path)
                
                # Adjust music duration to match video
                if music.duration > video.duration:
                    music = music.subclip(0, video.duration)
                elif music.duration < video.duration:
                    loops_needed = int(video.duration / music.duration) + 1
                    music = concatenate_audioclips([music] * loops_needed).subclip(0, video.duration)
                
                music = music.volumex(0.4)
                
                if video.audio:
                    final_audio = CompositeAudioClip([video.audio.volumex(0.6), music])
                else:
                    final_audio = music
                
                return video.set_audio(final_audio)
            else:
                print(f"⚠️  Music file not found: {music_file_path}")
                return video
        except Exception as e:
            print(f"⚠️  Error adding local music: {e}")
            return video

# Main execution functions
def create_ai_video(topic, output_name=None):
    """
    Create a complete AI-generated video from just a topic
    
    Args:
        topic: The subject/theme for your video (e.g., "ocean waves", "city life", "nature")
        output_name: Optional custom output filename
    """
    creator = AIVideoCreator()
    
    try:
        return creator.create_ai_video(topic, output_filename=output_name)
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        return None
    finally:
        creator.cleanup()

def create_custom_video(video_search, music_search, subtitles_list, output_name):
    """
    Create a video with specific parameters
    
    Args:
        video_search: Search term for Pexels video
        music_search: Search term for Pixabay music  
        subtitles_list: List of subtitle dicts with 'start', 'end', 'text'
        output_name: Output filename
    """
    creator = AIVideoCreator()
    
    try:
        return creator.create_video(video_search, music_search, subtitles_list, output_name)
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        return None
    finally:
        creator.cleanup()

if __name__ == "__main__":
    print("🎬 AI Video Creator")
    print("=" * 50)
    
    # Example 1: AI-generated content
    print("\n🤖 Creating AI-generated video...")
    topic = input("Enter a topic for your video (e.g., 'sunset over mountains'): ").strip()
    if topic:
        create_ai_video(topic)
    
    print("\n" + "=" * 50)
    
    # Example 2: Custom video
    choice = input("\nCreate another custom video? (y/n): ").strip().lower()
    if choice == 'y':
        video_search = input("Video search term: ").strip()
        music_search = input("Music search term: ").strip()
        
        # Simple subtitle creation
        print("\nCreate subtitles (press Enter twice when done):")
        custom_subs = []
        start_time = 0
        
        while True:
            text = input(f"Subtitle text (starts at {start_time}s): ").strip()
            if not text:
                break
            
            duration = float(input("Duration (seconds): ") or "3")
            custom_subs.append({
                "start": start_time,
                "end": start_time + duration,
                "text": text
            })
            start_time += duration
        
        if custom_subs:
            output_name = input("Output filename (or press Enter for default): ").strip()
            if not output_name:
                output_name = "custom_video.mp4"
            
            create_custom_video(video_search, music_search, custom_subs, output_name)

# Quick usage examples:
"""
# Simple AI video
create_ai_video("peaceful forest")

# Custom video with your own subtitles
my_subtitles = [
    {"start": 0, "end": 4, "text": "Welcome to our story"},
    {"start": 4, "end": 8, "text": "A journey begins here"},
    {"start": 8, "end": 12, "text": "Thanks for watching"}
]
create_custom_video("mountain landscape", "ambient music", my_subtitles, "my_video.mp4")
"""